import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { useDictionary } from '/@/store/modules/dictionary';
import { Image } from 'ant-design-vue';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import Tinymce from 'tinymce';

const dictionary = useDictionary();

const userStore = useUserStore();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '商品名称',
      dataIndex: 'productName',
    },
    {
      title: '一级商户',
      dataIndex: 'companyName',
      width: 200,
    },
    {
      title: '商品封面',
      dataIndex: 'productCoverImg',
      width: 90,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '商品栏目',
      dataIndex: 'integralProductColumn',
      width: 90,
      customRender: ({ text }) => {
        return (
          <span>{dictionary.getDictionaryMap.get(`integralProductColumn_${text}`)?.dictName}</span>
        );
      },
    },
    {
      title: '上架状态',
      dataIndex: 'state',
      width: 90,
      customRender: ({ text, record }) => {
        const { state } = record;
        return (
          <span class={state === 'up' ? 'text-green-500' : state == 'down' ? 'text-red-500' : ''}>
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'productName',
      label: '商品名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'integralProductColumn',
      label: '商品栏目',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('integralProductColumn'),
        };
      },
    },
    {
      field: 'state',
      label: '上架状态',
      component: 'RadioGroup',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            {
              label: '全部',
              value: undefined,
            },
            ...(dictionary.getDictionaryOpt.get('saleEnable') as RadioGroupChildOption[]),
          ],
        };
      },
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'productName',
      label: '商品名称',
      colProps: { span: 24 },
      labelWidth: 80,
      required: true,
      rulesMessageJoinLabel: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 100,
      },
    },
    {
      field: 'productType',
      label: '商品类型',
      colProps: { span: 12 },
      labelWidth: 80,
      required: true,
      component: 'RadioGroup',
      // dynamicDisabled({ values }) {
      //   return values.sourceType === 'inclusive' ? true : false
      // },
      slot: 'productType',
      // componentProps: function () {
      //   return {
      //     options: dictionary.getDictionaryOpt.get('productType') as CheckboxOptionType[],
      //     placeholder: '请选择商品类型',
      //   };
      // },
    },
    {
      field: 'integralPayment',
      label: '领取方式',
      colProps: { span: 12 },
      labelWidth: 80,
      required: true,
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      dynamicDisabled({ values }) {
        return values.consumeType === 'mix' ? true : false;
      },
      // slot: 'integralPayment',
      componentProps({ formModel }) {
        if ('virtual' === formModel.productType) {
          return { options: [{ label: '线下核销', value: '2' }] as RadioGroupChildOption[] };
        } else {
          return {
            options: dictionary.getDictionaryOpt.get('integralPayment') as RadioGroupChildOption[],
          };
        }
      },
    },
    {
      labelWidth: 80,
      field: 'sourceType',
      label: '商品来源',
      colProps: { span: 12 },
      required: true,
      ifShow: isInclusive !== true,
      component: 'CheckboxGroup',
      defaultValue: 'provide',
      slot: 'sourceType',
      /*      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('productSourcetype'),
          placeholder: '请选择商品来源',
        };
      },*/
    },
    //普惠商户 选择商品
    {
      labelWidth: 80,
      field: 'companyName',
      label: '一级商户',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      slot: 'productButton',
      ifShow({ values }) {
        return values.sourceType === 'inclusive' ? true : false;
      },
    },
    //自主提供选择商家
    {
      labelWidth: 80,
      field: 'companyName',
      label: '一级商户',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      slot: 'button',
      dynamicDisabled: isUpdate,
      ifShow({ values }) {
        return values.sourceType === 'inclusive' ? false : true && isUpdate === false;
      },
    },
    {
      labelWidth: 80,
      field: 'companyId',
      label: '商户id',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: isUpdate,
      show: false,
    },
    {
      labelWidth: 80,
      field: 'companyName',
      label: '一级商户',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: isUpdate,
      ifShow({ values }) {
        return values.sourceType === 'inclusive' ? false : true && isUpdate === true;
      },
    },
    {
      labelWidth: 80,
      field: 'parentAutoId',
      label: '一级商家autoid',
      required: true,
      colProps: { span: 12 },
      component: 'InputNumber',
      dynamicDisabled: isUpdate,
      show: false,
    },
    {
      labelWidth: 80,
      field: 'childProductCompanyName',
      label: '二级商户',
      required: false,
      colProps: { span: 24 },
      component: 'ApiSelect',
      slot: 'childButton',
      dynamicDisabled: isUpdate,
      ifShow({ values }) {
        return values.companyName !== undefined;
      },
    },
    {
      labelWidth: 80,
      field: 'childProductCompany',
      label: '二级商户ID',
      required: false,
      colProps: { span: 24 },
      component: 'ApiSelect',
      dynamicDisabled: isUpdate,
      show: false,
    },
    {
      labelWidth: 80,
      field: 'consumeType',
      label: '消耗类型',
      colProps: { span: 12 },
      required: true,
      ifShow: isInclusive !== true,
      component: 'CheckboxGroup',
      defaultValue: 'integral',
      slot: 'consumeType',
      /*      componentProps:{
          options: filter(
            cloneDeep (dictionary.getDictionaryOpt.get('consumeType')),
            v => v.value !== 'CNY',
          ),
          placeholder: '请选择消耗类型',
      },*/
    },
    {
      labelWidth: 120,
      field: 'maxReceiveNum',
      label: '最多领取次数',
      colProps: { span: 12 },
      ifShow: isInclusive !== true,
      rulesMessageJoinLabel: true,
      component: 'InputNumber',
      componentProps: {
        min: 1,
      },
    },
    {
      labelWidth: 85,
      field: 'productCoverImg',
      label: '商品封面图',
      colProps: { span: 12 },
      required: true,
      component: 'CropperForm',
      slot: 'picOnlyOne',
      dynamicDisabled({ values }) {
        return values.sourceType === 'inclusive' ? true : false;
      },
    },
    {
      labelWidth: 80,
      field: 'integralProductColumn',
      label: '商品栏目',
      colProps: { span: 24 },
      required: true,
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('integralProductColumn') as CheckboxOptionType[],
      },
    },
    {
      field: 'productPublicityImg',
      label: '宣传图',
      labelWidth: 60,
      colProps: { span: 24 },
      required: true,
      component: 'CropperForm',
      slot: 'pic',
      rest: true,
    },
    {
      field: 'contractPhone',
      label: '商家电话',
      colProps: { span: 12 },
      required: true,
      labelWidth: 80,
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      field: 'exchangeNotice',
      label: '兑换须知',
      labelWidth: 80,
      required: true,
      component: 'InputTextArea',
      colProps: { span: 24 },
      defaultValue:
        '兑后48小时内发货，确认兑换过后积分不退港澳台、海外、受其他外在影响或突发情况地区暂不支持发货。',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 500,
        autoSize: { minRows: 1, maxRows: 6 },
      },
    },
    {
      field: 'productIntroduce',
      label: '商品简介',
      colProps: { span: 24 },
      labelWidth: 80,
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      render({ model, field, disabled }) {
        return (
          <Tinymce
            value={model[field]}
            options={{ readonly: !!disabled }}
            onChange={value => {
              model[field] = value;
            }}
          ></Tinymce>
        );
      },
    },
    {
      field: 'address',
      required: true,
      label: '商户地址',
      component: 'MapSelect',
      labelWidth: 80,
      rest: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          onChangeLnglat: lnglat => (formModel['addressCoordinate'] = lnglat),
          lnglat: formModel['addressCoordinate'],
        };
      },
    },
    {
      field: 'addressCoordinate',
      label: '商户坐标',
      colProps: { span: 24 },
      component: 'ShowSpan',
      rulesMessageJoinLabel: true,
      show: false,
    },
  ];
};
