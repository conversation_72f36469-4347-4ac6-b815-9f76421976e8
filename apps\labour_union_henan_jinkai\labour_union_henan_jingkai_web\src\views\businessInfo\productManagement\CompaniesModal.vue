<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
    @cancel="handleCancel"
  >
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '选择',
              type: 'default',
              onClick: handleChoiceCompany.bind(null, record),
              ifShow: !show,
            },
          ]"
        />
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { computed, ref } from 'vue';
import { findList } from '@/api/productManagement';
import { queryCompanyList } from './data';

const emit = defineEmits(['success', 'register']);

const show = ref(false); //是否展示
const title = computed(() => {
  return `核销商户选择`;
});

const [registerModal, {}] = useModalInner(async date => {
  await clearSelectedRowKeys();
  await reload();
});

const [registerTable, { reload, getForm, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: findList,
  columns: [
    {
      title: '商户名称',
      dataIndex: 'companyName',
      width: 100,
    },
    {
      title: '商家封面',
      dataIndex: 'companyIcon',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '联系方式',
      dataIndex: 'contractPhone',
      width: 100,
    },
    {
      title: '所属区域',
      dataIndex: 'areaCode',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`regionCode_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '商户状态',
      dataIndex: 'state',
      width: 100,
      customRender: ({ text, record }) => {
        const { state } = record;
        return (
          <span
            class={state == 'normal' ? 'text-green-500' : state == 'down' ? 'text-red-500' : ''}
          >
            {dictionary.getDictionaryMap.get(`commonStatus_${text}`)?.dictName}
          </span>
        );
      },
    },
  ],
  beforeFetch: params => {
    params.companyType = 'merchant';
    params.pid = 0;
    return params;
  },
  formConfig: {
    labelWidth: 90,
    autoSubmitOnEnter: true,
    schemas: [
      {
        field: 'companyName',
        label: '商户名称',
        component: 'Input',
        colProps: { span: 8 },
        componentProps: {
          placeholder: '请输入商户名称',
          autocomplete: 'off',
        },
      },
    ],
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
  maxHeight: 400,
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
});

//选择按钮操作
function handleChoiceCompany(record) {
  emit('success', { record: record });
}

//关闭时执行
function handleCancel() {
  getForm()?.resetFields();
}
</script>
